<template>
  <div class="schedule-container">
    <van-sticky>
      <van-nav-bar
          v-if="isApp"
          title="进度填报"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />
      <van-dropdown-menu v-if="isBusiness">
        <van-dropdown-item v-model="organizeId" :options="projectOption"/>
      </van-dropdown-menu>
      <van-dropdown-menu>
        <van-dropdown-item v-model="statusValue" :options="statusOption" />
        <van-dropdown-item v-model="typeValue"   :options="typeOption" />
      </van-dropdown-menu>
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
          class="list-container"
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad">

        <div v-for="item in listData" :key="item.Progress_ID" @click="gotoDetails(item)">
          <van-swipe-cell>
            <div class="item-wrapper">
              <div class="left-wrapper">
                <van-image
                    height="88"
                    width="88"
                    :src="appendPhotoSrc(item.ListAttachment)"
                />
              </div>
              <div class="right-wrapper">
                <div>{{`填报人员：${item.Progress_createuser}`}}</div>
                <div>{{`填报日期：${item.Progress_creatime.split(' ')[0]}`}}</div>
                <div>{{`任务名称：${item.Progress_Name}`}}</div>
              </div>
              <div class="status-wrapper">
                <span :class="getStatusBg(item.AuditStatus)">{{getStatusStr(item.AuditStatus)}}</span>
              </div>
            </div>
            <template #right v-if="isCanAdd">
              <div class="item-right-wrapper">
                <img :src="require('../../assets/images/quality/delete-item.png')" alt="" @click="deleteItem(item)">
              </div>
            </template>
          </van-swipe-cell>
        </div>
      </van-list>
    </van-pull-refresh>
    <van-image
        class="add-image"
        width="60"
        height="60"
        :src="require('../../assets/images/common/add-btn.png')"
        @click="addSchedule"
    ></van-image>
  </div>

</template>
<script setup>
import router from "@/router";
import {onMounted, ref, watch} from "vue";
import api from "@/api";
import {getOrganizeId, setOrganizeId} from "@/utils/organizeId";
import {showToast} from "vant";
import {getToken, setToken} from "@/utils/token";
import {setUserInfo} from "@/utils/user";
import {useRoute} from "vue-router";
import {setAu} from "@/utils/au";

const statusValue = ref('');
const typeValue = ref('');
const statusOption = [
  { text: '全部状态', value: '' },
  { text: '待提交', value: '0' },
  { text: '待审核', value: '1' },
  { text: '已审核', value: '2' },
  { text: '已驳回', value: '3' }
];
const typeOption = ref([
  { text: '全部计划', value: '' }
]);
let loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
/**
 * 刷新列表
 */
function onRefresh() {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  // 加载数据
  getList();
  // 取消刷新状态
  refreshing.value = false
}
/**
 * 列表加载方法
 */
function onLoad(){
}
const listData = ref([])

/**
 * 拼接图片地址
 * @param df
 */
function appendPhotoSrc(df) {
  if (df.length > 0) {
    return window.IP_CONFIG.BASE_URL + '/' + df[df.length - 1].AttachmentUrl
  } else {
    return require('../../assets/images/schedule/default.png')
  }
}
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
  /*  try {
      if (navigator.userAgent.indexOf('Android') > -1){
        mjs.clickBack()
      }else if (navigator.userAgent.indexOf('iPhone') > -1) {
        window.webkit.messageHandlers.clickBack.postMessage(null)
      }
    }catch(error){

    }*/
}
/**
 * 获取全部计划
 */
async function getAllTree() {
  loading.value = true
  const res = await api.GetTree({
    Token: token.value,
    OrganizeId: organizeId.value,
  })
  if (res.data.Ret === 1){
    for (const data of res.data.Data) {
      const temp = {
        text : data.Name,
        value : data.UID
      }
      typeOption.value.push(temp)
    }
  }
  loading.value = false
}

/**
 * 获取列表数据
 */
async function getList() {
  loading.value = true
  const params = {}
  params.Token = token.value
  if (!typeValue.value){
    params.organizeId = organizeId.value
  }else {
    params.uid = typeValue.value
    params.projectid = typeValue.value
  }
  if (statusValue.value){
    params.auditStatus = statusValue.value
  }
  const res = await api.GetProject(params)
  if (res.data.Ret === 1){
    listData.value = res.data.Data
  }
  finished.value = true
  loading.value = false
}

const isCanAdd = ref(false)
const isCanAudit = ref(false)
/**
 * 获取权限数据
 */
async function getAu() {
  const res = await api.GetUserMenuTree({
    Token: getToken(),
    organizeId: getOrganizeId(),
    parentId: '0'
  })
  if (res.data.Ret === 1){
    const data = res.data.Data
    for (const datum of data) {
      if (datum.MenuCode === 'JDGL'){
        for (const datumElement of datum.Children) {
          if (datumElement.MenuCode === 'JDTB'){
            for (const datumElementElement of datumElement.Buttons) {
              if (datumElementElement.ButtonCode === 'JDTB_SubmitAudit'){
                // 可以填报
                isCanAdd.value = true
              }
              if (datumElementElement.ButtonCode === 'JDTB_Audit'){
                // 可以审核
                isCanAudit.value = true
              }
            }

          }
        }
      }
    }
    // 权限数据存储
    let au = false
    if (isCanAudit.value){
      au = false
    }
    if (isCanAdd.value){
      au = true
    }
    setAu(au)
  }
}

/**
 * 判断状态文字显示
 * @param str
 */
function getStatusStr(str){
  let result
  switch (str) {
    case 0:
      result =  '待提交'
      break
    case 1:
      result =  '待审核'
      break
    case 2:
      result =  '已审核'
      break
    case 3:
      result =  '已驳回'
      break
    default:
      break
  }
  return result
}

/**
 * 判断状态文字背景色
 * @param str
 */
function getStatusBg(str){
  return 'status-' + str
}

/**
 * 跳转路由到新建页面
 */
function addSchedule() {
  let au = false
  if (isCanAudit.value){
    au = false
  }
  if (isCanAdd.value){
    au = true
    router.push({
      path: '/addSchedule',
      query: {
        au
      }
    })
  }else {
    showToast('暂无权限')
  }
}

/**
 * 跳转路由到详情页面
 */
function gotoDetails(item) {
  router.push({
    path: '/scheduleDetails',
    query: {
      id: item.Progress_ID
    }
  })
}

/**
 * 删除单条数据
 * @param item
 * @returns {Promise<void>}
 */
async function deleteItem(item) {
 /* if (item.AuditStatus !== 0 ){
    showToast('进度已提交了，无法删除！')
    return
  }*/
  const res = await api.DeleteProject(token.value,item.Progress_ID)
  if (res.data.Ret === 1) {
    showToast('删除成功')
    await getList()
  } else {
    showToast(res.data.Msg)
  }
}

const token = ref('')
let isBusiness = ref(false)
let isApp = ref(false)

onMounted(() => {
  isBusiness = window.IP_CONFIG.IS_BUSINESS
  if (window.IP_CONFIG.IS_BUSINESS){
    let outerToken
    if (window.IP_CONFIG.IS_QUERY){
      const route = useRoute()
      outerToken = route.query.Token
    }else {
      // 获取外部token
      outerToken = location.search
      if (outerToken && outerToken.length > 7){
        outerToken = outerToken.slice(7)
      }else {
        outerToken = sessionStorage.getItem('token')
      }
    }
    // 获取BIMe-->Token
    getOuterToken(outerToken)
  }else {
    isApp = true
    const route = useRoute()
    const mToken = route.query.token
    if (!mToken) {
      token.value = getToken()
      console.log('路由地址Token为空')
    } else {
      token.value = mToken
      setToken(token.value)
      getBIMeUserInfo()
    }
    const mOrganizeId = route.query.organizeId
    if (!mOrganizeId) {
      organizeId.value = getOrganizeId()
      console.log('路由地址项目Id为空')
    } else {
      organizeId.value = mOrganizeId
      setOrganizeId(mOrganizeId)
    }
    getAu()
    getList()
  }
})
const projectOption = ref([])
const organizeId = ref('')

/**
 * 获取项目列表
 * @returns {Promise<void>}
 */
async function getProjectList() {
  const res = await api.GetProjectList({
    token: token.value,
    pageNum: 1,
    pageSize: 99999,
    keyword: ''
  })
  if (res.data.Ret === 1) {
    const projectList = res.data.Data.rows
    if (projectList.length > 0) {
      for (const projectListElement of projectList) {
        projectOption.value.push(
            {
              text: projectListElement.ProjectName,
              value: projectListElement.ProjectId
            }
        )
      }
      if (getOrganizeId()) {
        organizeId.value = getOrganizeId()
        // 不为空
      } else {
        organizeId.value = projectOption.value[0].value
        setOrganizeId(organizeId.value)
      }
      // await getList()
      // await getTypes()
    }
  }
}
/**
 * 获取外部token
 * @returns {Promise<void>}
 */
async function getOuterToken(outerToken) {
  const res = await api.LongyunLogin(outerToken)
  if (res.data.Ret === 1){
    token.value = res.data.Data.token
    setToken(token.value)
    await getProjectList()
    await getBIMeUserInfo()
    await getAu()
  }else {
    showToast(res.data.Msg)
  }
  console.log('获取token',res)
}
/**
 * 获取用户信息并保存
 */
async function getBIMeUserInfo() {
  const res = await api.GetUser({
    token: token.value
  })
  if (res.data.Ret === 1){
    setUserInfo(JSON.stringify(res.data.Data))
  }
}

watch(organizeId, async (newValue) => {
  setOrganizeId(newValue)
  await getAllTree()
  await getList()
  await getAu()
})
watch(typeValue, async (newValue) => {
  await getList()
})
watch(statusValue, async (newValue) => {
  await getList()
})
</script>

<style scoped lang="scss">
.schedule-container {
  background-color: #f2f2f2;
  .list-container{
    margin: 16px;

    .item-wrapper {
      position: relative;
      margin-bottom: 16px;
      height: 112px;
      display: flex;
      border-radius: 4px;
      background-color: white;
      .left-wrapper{
        margin-top: 11px;
        margin-left: 11px;
        margin-right: 11px;
        width: 90px;
      }
      .right-wrapper{
        position: relative;
        margin-top: 11px;
        width: calc(100% - 116px);
        div{
          height: 33%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .status-wrapper{
        font-size: 13px;
        position: absolute;
        right: 0;
        top: 6px;
        color: white;
        .status-0{
          padding: 3px 10px 3px 10px;
          background-color: #FF9C00;
          border-radius: 16px 0 0 16px;
        }
        .status-1{
          padding: 3px 10px 3px 10px;
          background-color: #7a6df9;
          border-radius: 16px 0 0 16px;
        }
        .status-2{
          padding: 3px 10px 3px 10px;
          background-color: #43CAF4;
          border-radius: 16px 0 0 16px;
        }
        .status-3{
          padding: 3px 10px 3px 10px;
          background-color: #ff6b50;
          border-radius: 16px 0 0 16px;
        }
      }
    }
  }


  .item-right-wrapper{
    height: 100%;
    img{
      width: auto;
      height: auto;
      max-width: 100%;
      max-height: 100%;

    }
  }
  .add-image{
    position: fixed;
    bottom: 100px;
    right: 16px;
  }
}

</style>